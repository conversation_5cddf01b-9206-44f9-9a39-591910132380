<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="シナリオ管理" />
    <div class="px-6 pt-6">
      <BaseTable
        title="チャットボットシナリオ"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalScenariosCount"
      >
        <template #header-right>
          <UButton
            size="md"
            label="新規作成"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="createScenario"
          />
        </template>
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="refreshScenarios"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="paginatedScenarios"
          v-model:sort="sort"
          sort-mode="manual"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          :loading="loading"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-heroicons-document-text" class="text-gray-400 text-3xl" />
              <span class="text-sm text-gray-400">
                シナリオがありません
              </span>
            </div>
          </template>
          <template #name-data="{ row }">
            <div class="flex items-center space-x-3">
              <UIcon name="i-heroicons-document-text" class="text-blue-500" />
              <div>
                <div class="font-semibold">{{ row.name }}</div>
                <p class="text-xs text-gray-500">{{ row.description || '説明なし' }}</p>
              </div>
            </div>
          </template>
          <template #description-data="{ row }">
            <div class="max-w-xs">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ row.description || '説明なし' }}
              </p>
            </div>
          </template>
          <template #questions-data="{ row }">
            <div class="text-center">
              <span class="text-lg font-semibold">{{ row.questions?.length || 0 }}</span>
              <p class="text-xs text-gray-500">設問</p>
              <div v-if="row.questions && row.questions.length > 0" class="mt-1">
                <UBadge
                  v-if="row.questions.some(q => q.isFirstQuestion)"
                  color="yellow"
                  variant="soft"
                  size="xs"
                >
                  設定済み
                </UBadge>
                <UBadge v-else color="gray" variant="soft" size="xs">
                  未設定
                </UBadge>
              </div>
            </div>
          </template>
          <template #status-data="{ row }">
            <UBadge
              :color="row.isActive ? 'green' : 'gray'"
              variant="soft"
              size="sm"
            >
              {{ row.isActive ? '有効' : '無効' }}
            </UBadge>
          </template>
          <template #createdAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.createdAt) }}
              </span>
            </div>
          </template>
          <template #updatedAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.updatedAt) }}
              </span>
            </div>
          </template>
          <template #action-data="{ row }">
            <div class="flex space-x-2">
              <UButton
                icon="i-heroicons-pencil-square"
                size="xs"
                color="primary"
                variant="soft"
                label="編集"
                @click="editScenario(row)"
              />
              <UButton
                :icon="row.isActive ? 'i-heroicons-pause' : 'i-heroicons-play'"
                size="xs"
                :color="row.isActive ? 'red' : 'green'"
                variant="soft"
                :label="row.isActive ? '無効化' : '有効化'"
                @click="toggleScenario(row)"
              />
              <UButton
                icon="i-heroicons-document-duplicate"
                size="xs"
                color="gray"
                variant="soft"
                label="複製"
                @click="duplicateScenario(row)"
              />
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>

    <!-- Create/Edit Modal -->
    <UModal v-model="showModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingScenario ? 'シナリオ編集' : '新規シナリオ作成' }}
          </h3>
        </template>
        
        <div class="space-y-4">
          <UFormGroup label="シナリオ名" required>
            <UInput v-model="scenarioForm.name" placeholder="例: 初回問い合わせ対応" />
          </UFormGroup>
          <UFormGroup label="説明">
            <UTextarea
              v-model="scenarioForm.description"
              placeholder="このシナリオの説明を入力してください"
              :rows="3"
            />
          </UFormGroup>

          <!-- Scenario Builder Section -->
          <div v-if="editingScenario || scenarioForm.name" class="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">シナリオ設定</h4>
              <UButton
                size="sm"
                variant="soft"
                color="blue"
                icon="i-heroicons-cog-6-tooth"
                @click="openScenarioBuilder"
              >
                設定
              </UButton>
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              <div v-if="scenarioForm.questions && scenarioForm.questions.length > 0">
                <p class="mb-2">設問数: {{ scenarioForm.questions.length }}個</p>
                <div class="space-y-1">
                  <div v-for="(question, index) in scenarioForm.questions.slice(0, 3)" :key="question.id" class="flex items-center space-x-2">
                    <UBadge
                      :color="question.isFirstQuestion ? 'yellow' : 'gray'"
                      variant="soft"
                      size="xs"
                    >
                      {{ index + 1 }}
                    </UBadge>
                    <span class="truncate">{{ question.text || '未設定' }}</span>
                  </div>
                  <div v-if="scenarioForm.questions.length > 3" class="text-xs text-gray-500">
                    他{{ scenarioForm.questions.length - 3 }}個の設問...
                  </div>
                </div>
              </div>
              <div v-else class="text-center py-4">
                <UIcon name="i-heroicons-document-text" class="text-gray-400 text-2xl mb-2" />
                <p>まだ設問が設定されていません</p>
                <p class="text-xs">「設定」ボタンをクリックして設問を追加してください</p>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="soft" @click="closeModal">
              キャンセル
            </UButton>
            <UButton
              color="primary"
              @click="saveScenario"
              :loading="saving"
              :disabled="!scenarioForm.name || !scenarioForm.questions || scenarioForm.questions.length === 0"
            >
              {{ editingScenario ? '更新' : '作成' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Scenario Builder Modal -->
    <UModal v-model="showBuilderModal" :ui="{ width: 'sm:max-w-6xl' }">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">シナリオビルダー</h3>
            <div class="text-sm text-gray-500">
              {{ scenarioForm.name || '新規シナリオ' }}
            </div>
          </div>
        </template>

        <div class="space-y-6">
          <!-- View Toggle -->
          <div class="flex items-center justify-between">
            <h4 class="text-md font-medium">シナリオ設定</h4>
            <div class="flex items-center space-x-3">
              <UButtonGroup size="sm">
                <UButton
                  :variant="viewMode === 'list' ? 'solid' : 'soft'"
                  icon="i-heroicons-list-bullet"
                  @click="viewMode = 'list'"
                >
                  リスト表示
                </UButton>
                <UButton
                  :variant="viewMode === 'map' ? 'solid' : 'soft'"
                  icon="i-heroicons-squares-2x2"
                  @click="viewMode = 'map'"
                >
                  マップ表示
                </UButton>
              </UButtonGroup>
              <UButton
                size="sm"
                variant="soft"
                icon="i-heroicons-plus"
                @click="addQuestion"
              >
                設問を追加
              </UButton>
            </div>
          </div>

          <!-- Map View -->
          <div v-if="viewMode === 'map'" class="border rounded-lg" style="height: 600px;">
            <ScenarioMap
              :questions="scenarioForm.questions"
              @edit-question="editQuestion"
              @edit-choice="editChoiceFromMap"
              @delete-question="removeQuestion"
            />
          </div>

          <!-- List View -->
          <div v-else class="space-y-4">
            <div class="flex items-center justify-between">
              <h4 class="text-md font-medium">設問一覧</h4>
            </div>

            <div v-if="scenarioForm.questions && scenarioForm.questions.length > 0" class="space-y-4">
              <div
                v-for="(question, questionIndex) in scenarioForm.questions"
                :key="question.id"
                :class="[
                  'border rounded-lg p-4',
                  question.isFirstQuestion ? 'border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20' : 'border-gray-200 dark:border-gray-700'
                ]"
              >
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center space-x-2">
                    <UBadge
                      :color="question.isFirstQuestion ? 'yellow' : 'gray'"
                      variant="soft"
                    >
                      設問 {{ questionIndex + 1 }}
                      {{ question.isFirstQuestion ? '(最初)' : '' }}
                    </UBadge>
                    <UButton
                      v-if="!question.isFirstQuestion"
                      size="xs"
                      variant="soft"
                      color="yellow"
                      @click="setAsFirstQuestion(question.id)"
                    >
                      最初に設定
                    </UButton>
                  </div>
                  <div class="flex space-x-2">
                    <UButton
                      size="xs"
                      variant="soft"
                      icon="i-heroicons-pencil"
                      @click="editQuestion(questionIndex)"
                    >
                      編集
                    </UButton>
                    <UButton
                      size="xs"
                      variant="soft"
                      color="red"
                      icon="i-heroicons-trash"
                      @click="removeQuestion(questionIndex)"
                    >
                      削除
                    </UButton>
                  </div>
                </div>

                <div class="space-y-3">
                  <div>
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">設問内容</label>
                    <div class="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm">
                      {{ question.text || '未設定' }}
                    </div>
                  </div>

                  <div>
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">選択肢 ({{ question.choices.length }}個)</label>
                    <div class="mt-1 space-y-2">
                      <div
                        v-for="(choice, choiceIndex) in question.choices"
                        :key="choice.id"
                        class="p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm"
                      >
                        <div class="flex items-center justify-between">
                          <span class="font-medium">{{ choiceIndex + 1 }}. {{ choice.text || '未設定' }}</span>
                          <UBadge variant="soft" size="xs">
                            {{ getActionLabel(choice.action.type) }}
                          </UBadge>
                        </div>
                        <div v-if="choice.responseMessage" class="text-xs text-gray-500 mt-1">
                          返答: {{ choice.responseMessage }}
                        </div>
                      </div>
                      <div v-if="question.choices.length === 0" class="text-xs text-gray-500 italic">
                        選択肢が設定されていません
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
              <UIcon name="i-heroicons-document-text" class="text-gray-400 text-3xl mb-2" />
              <p class="text-gray-500">設問がありません</p>
              <p class="text-sm text-gray-400">「設問を追加」ボタンをクリックして最初の設問を作成してください</p>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="soft" @click="closeBuilderModal">
              閉じる
            </UButton>
            <UButton color="primary" @click="saveScenarioBuilder">
              保存
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Question Edit Modal -->
    <UModal v-model="showQuestionModal" :ui="{ width: 'sm:max-w-4xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingQuestionIndex !== null ? '設問編集' : '新規設問作成' }}
          </h3>
        </template>

        <div class="space-y-6">
          <UFormGroup label="設問内容" required>
            <UTextarea
              v-model="questionForm.text"
              placeholder="設問を100文字以内で入力してください"
              :rows="3"
              :maxlength="100"
            />
            <template #help>
              <div class="flex justify-between text-xs">
                <span class="text-gray-500">設問は100文字以内で登録してください。</span>
                <span :class="questionForm.text?.length > 100 ? 'text-red-500' : 'text-gray-500'">
                  {{ questionForm.text?.length || 0 }}/100
                </span>
              </div>
            </template>
          </UFormGroup>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">選択肢</h4>
              <UButton
                v-if="questionForm.choices.length < 4"
                size="xs"
                variant="soft"
                icon="i-heroicons-plus"
                @click="addChoice"
              >
                選択肢を追加
              </UButton>
            </div>

            <div v-for="(choice, index) in questionForm.choices" :key="choice.id" class="border rounded-lg p-4 space-y-3">
              <div class="flex items-center justify-between">
                <h5 class="text-sm font-medium">選択肢{{ index + 1 }}</h5>
                <UButton
                  v-if="questionForm.choices.length > 1"
                  size="xs"
                  variant="soft"
                  color="red"
                  icon="i-heroicons-trash"
                  @click="removeChoice(index)"
                />
              </div>

              <UFormGroup label="選択肢内容" required>
                <UTextarea
                  v-model="choice.text"
                  placeholder="選択肢として表示する内容を入力してください"
                  :rows="2"
                  :maxlength="150"
                />
                <template #help>
                  <div class="flex justify-between text-xs">
                    <span class="text-gray-500">選択肢は全角150文字以内で登録してください。</span>
                    <span :class="choice.text?.length > 150 ? 'text-red-500' : 'text-gray-500'">
                      {{ choice.text?.length || 0 }}/150
                    </span>
                  </div>
                </template>
              </UFormGroup>

              <UFormGroup label="返答メッセージ（任意）">
                <UTextarea
                  v-model="choice.responseMessage"
                  placeholder="選択肢が選ばれたときに返すメッセージ（任意）"
                  :rows="2"
                />
              </UFormGroup>

              <UFormGroup label="選択肢押下時の挙動" required>
                <USelect
                  v-model="choice.action.type"
                  :options="actionOptions"
                  placeholder="挙動を選択"
                />
              </UFormGroup>

              <!-- Additional fields based on action type -->
              <UFormGroup
                v-if="choice.action.type === 'select_end_template'"
                label="終了テンプレート"
              >
                <USelect
                  v-model="choice.action.endTemplateId"
                  :options="endTemplateOptions"
                  placeholder="終了テンプレートを選択"
                />
              </UFormGroup>

              <UFormGroup
                v-if="choice.action.type === 'next_question'"
                label="次の設問"
              >
                <USelect
                  v-model="choice.action.nextQuestionId"
                  :options="nextQuestionOptions"
                  placeholder="次の設問を選択"
                />
              </UFormGroup>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="soft" @click="closeQuestionModal">
              キャンセル
            </UButton>
            <UButton
              color="primary"
              @click="saveQuestion"
              :disabled="!isQuestionFormValid"
            >
              保存
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { Scenario, ScenarioQuestion, ScenarioChoice, ScenarioAction, ScenarioActionType } from '@/types'
import ScenarioMap from '@/components/app/chatbot/ScenarioMap.vue'

definePageMeta({
  navigationPage: "app-chatbot-scenario",
  middleware: ["permissions"],
});

const showModal = ref(false)
const showBuilderModal = ref(false)
const showQuestionModal = ref(false)
const saving = ref(false)
const loading = ref(false)
const editingScenario = ref<Scenario | null>(null)
const editingQuestionIndex = ref<number | null>(null)
const viewMode = ref<'list' | 'map'>('map')

// Pagination
const pagination = ref({
  page: 1,
  pageRangeDisplayed: 10
})

// Helper function to generate unique IDs
function generateId(): string {
  return 'id_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

const scenarios = ref<Scenario[]>([
  {
    id: '1',
    name: '初回問い合わせ対応',
    description: '初めて問い合わせをするユーザー向けの基本的な対応フロー',
    questions: [
      {
        id: 'q1',
        text: 'どのようなご用件でしょうか？',
        isFirstQuestion: true,
        order: 1,
        choices: [
          {
            id: 'c1',
            text: '製品について質問したい',
            responseMessage: '製品についてお答えします。',
            action: { type: 'next_question', nextQuestionId: 'q2' }
          },
          {
            id: 'c2',
            text: 'サポートが必要',
            responseMessage: '担当者におつなぎします。',
            action: { type: 'open_talk' }
          }
        ]
      },
      {
        id: 'q2',
        text: 'どちらの製品についてお聞きになりたいですか？',
        isFirstQuestion: false,
        order: 2,
        choices: [
          {
            id: 'c3',
            text: '製品A',
            responseMessage: '製品Aについてご案内します。',
            action: { type: 'select_end_template', endTemplateId: '1' }
          },
          {
            id: 'c4',
            text: '製品B',
            responseMessage: '製品Bについてご案内します。',
            action: { type: 'select_end_template', endTemplateId: '2' }
          }
        ]
      }
    ],
    isActive: true,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20'
  },
  {
    id: '2',
    name: 'FAQ自動応答',
    description: 'よくある質問に対する自動応答シナリオ',
    questions: [
      {
        id: 'q3',
        text: 'よくある質問からお選びください',
        isFirstQuestion: true,
        order: 1,
        choices: [
          {
            id: 'c5',
            text: '営業時間について',
            responseMessage: '営業時間は平日9:00-18:00です。',
            action: { type: 'end_scenario' }
          },
          {
            id: 'c6',
            text: '料金について',
            responseMessage: '料金についてご案内します。',
            action: { type: 'show_end_survey' }
          }
        ]
      }
    ],
    isActive: true,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18'
  }
])

// Table columns
const columns = [
  {
    key: "name",
    label: "シナリオ名",
    sortable: true,
  },
  {
    key: "description",
    label: "説明",
    sortable: false,
  },
  {
    key: "questions",
    label: "設問数",
    sortable: true,
  },
  {
    key: "status",
    label: "ステータス",
    sortable: true,
  },
  {
    key: "createdAt",
    label: "作成日時",
    sortable: true,
  },
  {
    key: "updatedAt",
    label: "更新日時",
    sortable: true,
  },
  {
    label: "#",
    key: "action",
    class: "text-center w-0",
  },
]

// Sorting
const sort = ref({
  column: 'createdAt',
  direction: 'desc' as 'asc' | 'desc'
})

// Computed properties for pagination
const totalScenariosCount = computed(() => scenarios.value.length)

const paginatedScenarios = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageRangeDisplayed
  const end = start + pagination.value.pageRangeDisplayed

  // Apply sorting
  const sorted = [...scenarios.value].sort((a, b) => {
    const aVal = a[sort.value.column as keyof typeof a]
    const bVal = b[sort.value.column as keyof typeof b]

    if (aVal < bVal) return sort.value.direction === 'asc' ? -1 : 1
    if (aVal > bVal) return sort.value.direction === 'asc' ? 1 : -1
    return 0
  })

  return sorted.slice(start, end)
})

const pageFrom = computed(() => {
  return (pagination.value.page - 1) * pagination.value.pageRangeDisplayed + 1
})

const pageTo = computed(() => {
  const end = pagination.value.page * pagination.value.pageRangeDisplayed
  return Math.min(end, totalScenariosCount.value)
})

const scenarioForm = reactive({
  name: '',
  description: '',
  questions: [] as ScenarioQuestion[]
})

const questionForm = reactive({
  text: '',
  choices: [] as ScenarioChoice[]
})

// Action type labels
const actionLabels: Record<ScenarioActionType, string> = {
  'show_end_survey': '終了アンケート表示',
  'select_end_template': '終了テンプレート選択',
  'open_talk': 'トーク開設',
  'next_question': '次の設問へ',
  'end_scenario': 'シナリオ終了'
}

const getActionLabel = (actionType: ScenarioActionType): string => {
  return actionLabels[actionType] || actionType
}

// Options for dropdowns
const actionOptions = [
  { label: '終了アンケートを表示する', value: 'show_end_survey' },
  { label: '終了テンプレートを選択', value: 'select_end_template' },
  { label: 'トークを開設する', value: 'open_talk' },
  { label: '次の設問に進む', value: 'next_question' },
  { label: '終了する', value: 'end_scenario' }
]

const endTemplateOptions = [
  { label: '問題解決完了テンプレート', value: '1' },
  { label: '情報提供完了テンプレート', value: '2' },
  { label: '営業時間外対応テンプレート', value: '3' }
]

const nextQuestionOptions = computed(() => {
  return scenarioForm.questions.map((q, index) => ({
    label: `設問${index + 1}: ${q.text || '未設定'}`,
    value: q.id
  }))
})

// Validation
const isQuestionFormValid = computed(() => {
  if (!questionForm.text.trim() || questionForm.text.length > 100) return false
  if (questionForm.choices.length === 0) return false

  for (const choice of questionForm.choices) {
    if (!choice.text.trim() || choice.text.length > 150) return false
    if (!choice.action.type) return false

    if (choice.action.type === 'select_end_template' && !choice.action.endTemplateId) return false
    if (choice.action.type === 'next_question' && !choice.action.nextQuestionId) return false
  }

  return true
})

const formatDateTime = (date: string | Date) => {
  if (!date) return ''

  const dateObj = typeof date === 'string' ? new Date(date) : date

  if (isNaN(dateObj.getTime())) {
    console.warn('Invalid date:', date)
    return ''
  }

  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj)
}

// Scenario Builder Functions
const openScenarioBuilder = () => {
  showBuilderModal.value = true
}

const closeBuilderModal = () => {
  showBuilderModal.value = false
}

const addQuestion = () => {
  editingQuestionIndex.value = null
  Object.assign(questionForm, {
    text: '',
    choices: [{
      id: generateId(),
      text: '',
      responseMessage: '',
      action: { type: 'end_scenario' }
    }]
  })
  showQuestionModal.value = true
}

const removeQuestion = (index: number) => {
  const question = scenarioForm.questions[index]
  if (question.isFirstQuestion && scenarioForm.questions.length > 1) {
    // If removing the first question, make the next one first
    scenarioForm.questions[1].isFirstQuestion = true
  }
  scenarioForm.questions.splice(index, 1)

  // Update order
  scenarioForm.questions.forEach((q, i) => {
    q.order = i + 1
  })
}

const setAsFirstQuestion = (questionId: string) => {
  scenarioForm.questions.forEach(q => {
    q.isFirstQuestion = q.id === questionId
  })
}

const editQuestion = (index: number) => {
  editingQuestionIndex.value = index
  const question = scenarioForm.questions[index]

  Object.assign(questionForm, {
    text: question.text,
    choices: question.choices.map(c => ({ ...c }))
  })

  showQuestionModal.value = true
}

const saveScenarioBuilder = () => {
  // Validation can be added here
  closeBuilderModal()
}

// Question Modal Functions
const closeQuestionModal = () => {
  showQuestionModal.value = false
  editingQuestionIndex.value = null
  Object.assign(questionForm, {
    text: '',
    choices: []
  })
}

const saveQuestion = () => {
  if (!isQuestionFormValid.value) return

  if (editingQuestionIndex.value !== null) {
    // Update existing question
    const question = scenarioForm.questions[editingQuestionIndex.value]
    question.text = questionForm.text
    question.choices = questionForm.choices.map(c => ({ ...c }))
  } else {
    // Add new question
    const newQuestion: ScenarioQuestion = {
      id: generateId(),
      text: questionForm.text,
      choices: questionForm.choices.map(c => ({ ...c })),
      isFirstQuestion: scenarioForm.questions.length === 0,
      order: scenarioForm.questions.length + 1
    }
    scenarioForm.questions.push(newQuestion)
  }

  closeQuestionModal()
}

// Choice Management Functions
const addChoice = () => {
  if (questionForm.choices.length < 4) {
    questionForm.choices.push({
      id: generateId(),
      text: '',
      responseMessage: '',
      action: { type: 'end_scenario' }
    })
  }
}

const removeChoice = (index: number) => {
  if (questionForm.choices.length > 1) {
    questionForm.choices.splice(index, 1)
  }
}

// Map interaction functions
const editChoiceFromMap = (questionIndex: number, choiceIndex: number) => {
  // Open question edit modal and focus on the specific choice
  editQuestion(questionIndex)
  // Could add logic to scroll to or highlight the specific choice
}

const refreshScenarios = () => {
  loading.value = true
  // Simulate API call
  setTimeout(() => {
    loading.value = false
    const toast = useToast()
    toast.add({
      title: 'シナリオ一覧を更新しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }, 1000)
}

const createScenario = () => {
  editingScenario.value = null
  Object.assign(scenarioForm, {
    name: '',
    description: '',
    questions: []
  })
  showModal.value = true
}

const editScenario = (scenario: Scenario) => {
  editingScenario.value = scenario
  Object.assign(scenarioForm, {
    name: scenario.name,
    description: scenario.description,
    questions: scenario.questions.map(q => ({
      ...q,
      choices: q.choices.map(c => ({ ...c }))
    }))
  })
  showModal.value = true
}

const saveScenario = async () => {
  saving.value = true
  try {
    // TODO: Implement API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (editingScenario.value) {
      // Update existing scenario
      Object.assign(editingScenario.value, {
        name: scenarioForm.name,
        description: scenarioForm.description,
        questions: scenarioForm.questions.map(q => ({
          ...q,
          choices: q.choices.map(c => ({ ...c }))
        })),
        updatedAt: new Date().toISOString()
      })
    } else {
      // Create new scenario
      const newScenario: Scenario = {
        id: generateId(),
        name: scenarioForm.name,
        description: scenarioForm.description,
        questions: scenarioForm.questions.map(q => ({
          ...q,
          choices: q.choices.map(c => ({ ...c }))
        })),
        isActive: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      scenarios.value.push(newScenario)
    }

    closeModal()

    const toast = useToast()
    toast.add({
      title: '保存完了',
      description: `シナリオを${editingScenario.value ? '更新' : '作成'}しました。`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'シナリオの保存に失敗しました。',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  editingScenario.value = null
}

const toggleScenario = async (scenario: Scenario) => {
  scenario.isActive = !scenario.isActive
  scenario.updatedAt = new Date().toISOString()

  const toast = useToast()
  toast.add({
    title: scenario.isActive ? 'シナリオを有効化しました' : 'シナリオを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const duplicateScenario = (scenario: Scenario) => {
  const newScenario: Scenario = {
    ...scenario,
    id: generateId(),
    name: `${scenario.name} (コピー)`,
    questions: scenario.questions.map(q => ({
      ...q,
      id: generateId(),
      choices: q.choices.map(c => ({
        ...c,
        id: generateId()
      }))
    })),
    isActive: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  scenarios.value.push(newScenario)

  const toast = useToast()
  toast.add({
    title: 'シナリオを複製しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const deleteScenario = (scenario) => {
  const index = scenarios.value.findIndex(s => s.id === scenario.id)
  if (index > -1) {
    scenarios.value.splice(index, 1)
    
    const toast = useToast()
    toast.add({
      title: 'シナリオを削除しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }
}
</script>
