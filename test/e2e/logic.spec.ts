import { setup, createPage } from '@nuxt/test-utils/e2e'
import { describe, it, expect, beforeAll } from 'vitest'

describe('Login Page E2E Tests', () => {
  beforeAll(async () => {
    await setup({
      host: 'http://localhost:3000',
    })
  })

  describe('UI Elements Visibility', () => {
    it('should display all login form elements', async () => {
      const page = await createPage('/login')
      
      // Check main elements
      expect(await page.getByTestId('login-title').isVisible()).toBe(true)
      expect(await page.getByTestId('login-form').isVisible()).toBe(true)
      
      // Check form fields
      expect(await page.getByTestId('username').isVisible()).toBe(true)
      expect(await page.getByTestId('password').isVisible()).toBe(true)
      
      // Check other elements
      expect(await page.getByTestId('toggle-password').isVisible()).toBe(true)
      expect(await page.getByTestId('remember-me').isVisible()).toBe(true)
      expect(await page.getByTestId('forgot-password-link').isVisible()).toBe(true)
      expect(await page.getByTestId('login-submit').isVisible()).toBe(true)
    })

    it('should display correct labels and placeholders', async () => {
      const page = await createPage('/login')
      
      // Check that input fields have correct placeholders
      const usernameInput = page.getByTestId('username')
      const passwordInput = page.getByTestId('password')
      
      expect(await usernameInput.getAttribute('placeholder')).toContain('login ID')
      expect(await passwordInput.getAttribute('placeholder')).toContain('password')
    })
  })

  describe('Form Validation', () => {
    it('should show validation errors for empty required fields', async () => {
      const page = await createPage('/login')
      
      // Try to submit without filling required fields
      await page.getByTestId('login-submit').click()
      
      // Wait a bit for validation to show
      await page.waitForTimeout(500)
      
      // Check if form validation prevents submission (form should still be visible)
      expect(await page.getByTestId('login-form').isVisible()).toBe(true)
    })

    it('should validate minimum username length', async () => {
      const page = await createPage('/login')
      
      // Fill username with less than 6 characters
      await page.getByTestId('username').fill('short')
      await page.getByTestId('password').fill('validpassword123')
      await page.getByTestId('login-submit').click()
      
      // Wait for validation
      await page.waitForTimeout(500)
      
      // Form should still be visible (validation failed)
      expect(await page.getByTestId('login-form').isVisible()).toBe(true)
    })

    it('should validate minimum password length', async () => {
      const page = await createPage('/login')
      
      // Fill password with less than 8 characters
      await page.getByTestId('username').fill('validusername')
      await page.getByTestId('password').fill('short')
      await page.getByTestId('login-submit').click()
      
      // Wait for validation
      await page.waitForTimeout(500)
      
      // Form should still be visible (validation failed)
      expect(await page.getByTestId('login-form').isVisible()).toBe(true)
    })
  })

  describe('Interactive Elements', () => {
    it('should toggle password visibility', async () => {
      const page = await createPage('/login')
      
      const passwordInput = page.getByTestId('password')
      const toggleButton = page.getByTestId('toggle-password')
      
      // Initially password should be hidden
      expect(await passwordInput.getAttribute('type')).toBe('password')
      
      // Click toggle button
      await toggleButton.click()
      
      // Password should now be visible
      expect(await passwordInput.getAttribute('type')).toBe('text')
      
      // Click again to hide
      await toggleButton.click()
      
      // Password should be hidden again
      expect(await passwordInput.getAttribute('type')).toBe('password')
    })

    it('should handle remember me checkbox', async () => {
      const page = await createPage('/login')
      
      const rememberMeCheckbox = page.getByTestId('remember-me')
      
      // Initially should be unchecked
      expect(await rememberMeCheckbox.isChecked()).toBe(false)
      
      // Click to check
      await rememberMeCheckbox.click()
      
      // Should now be checked
      expect(await rememberMeCheckbox.isChecked()).toBe(true)
    })

    it('should navigate to forgot password page', async () => {
      const page = await createPage('/login')
      
      // Click forgot password link
      await page.getByTestId('forgot-password-link').click()
      
      // Should navigate to forgot password page
      await page.waitForURL('**/forgot-password')
      expect(page.url()).toContain('/forgot-password')
    })
  })

  describe('Form Submission', () => {
    it('should accept valid login credentials format', async () => {
      const page = await createPage('/login')
      
      // Fill with valid format credentials
      await page.getByTestId('username').fill('testuser123')
      await page.getByTestId('password').fill('testpassword123')
      
      // Submit the form
      await page.getByTestId('login-submit').click()
      
      // Wait for potential navigation or error
      await page.waitForTimeout(1000)
      
      // Note: In a real test, you might mock the auth service or test with valid credentials
      // For now, we just ensure the form accepts the input format
    })

    it('should show loading state during submission', async () => {
      const page = await createPage('/login')
      
      // Fill valid credentials
      await page.getByTestId('username').fill('testuser123')
      await page.getByTestId('password').fill('testpassword123')
      
      // Submit form
      await page.getByTestId('login-submit').click()
      
      // Check if button shows loading state (this might be brief)
      // Note: This test might need adjustment based on actual loading implementation
      const submitButton = page.getByTestId('login-submit')
      // You might need to check for loading class or disabled state
    })
  })

  describe('Error Handling', () => {
    it('should not show login error initially', async () => {
      const page = await createPage('/login')
      
      // Error alert should not be visible initially
      const errorAlert = page.getByTestId('login-error')
      expect(await errorAlert.isVisible()).toBe(false)
    })
  })

  describe('URL Parameters', () => {
    it('should pre-fill username from URL query parameter', async () => {
      const page = await createPage('/login?username=prefilleduser')
      
      // Username field should be pre-filled
      const usernameInput = page.getByTestId('username')
      expect(await usernameInput.inputValue()).toBe('prefilleduser')
    })
  })
})